%% State-Space Model Identification with Online Parameter Adaptation: Compare RLS Update Interval
% 比较rls_update_interval为1、10、50时模型性能的差异
% 新策略：周期性块更新 (Periodic Block Update)
clear;
close all;
clc;

%% 1. 数据加载（与原脚本一致）
db_filename = './webApi/data/measure_20250601.sqlite';
[~, name, ~] = fileparts(db_filename);
mat_filename = ['./data/' name '.mat'];

if exist(mat_filename, 'file')
    disp('Loading existing measures_df data...');
    load(mat_filename, 'measures_df');
else
    disp('Loading data from database...');
    conn = sqlite(db_filename);
    sql_query = ['SELECT speed, measureEntryTemp, measureExitTemp, setpointExitTemp, ' ...
                 'thick1, width1, weldPercent1, thick2, width2, weldPercent2, ' ...
                 'hd1, hd2, hd3, hd4, hd5, hd6, hd7, hd8, hd9, hd10, ' ...
                 'hd11, hd12, hd13, hd14, hd15, hd16, hd17, hd18, hd19, hd20, ' ...
                 'hd21, hd22, hd23, hd24, hd25, ' ...
                 'tt1, tt2, tt3, tt4, tt5, tt6, tt7, tt8, tt9, tt10, ' ...
                 'tt11, tt12, tt13, tt14, tt15, tt16, tt17, tt18, tt19, tt20, ' ...
                 'tt21, tt22, tt23, tt24, tt25 ' ...
                 'FROM measures'];
    measures_df = fetch(conn, sql_query);
    close(conn);
    avg_power = ((measures_df.hd1 + measures_df.hd25) * 700 + ...
                (measures_df.hd9 + measures_df.hd23) * 13 * 165 + ...
                (measures_df.hd2 + measures_df.hd3 + measures_df.hd4 + measures_df.hd5 + ...
                 measures_df.hd6 + measures_df.hd7 + measures_df.hd8 + measures_df.hd10 + ...
                 measures_df.hd11 + measures_df.hd12 + measures_df.hd13 + measures_df.hd14 + ...
                 measures_df.hd15 + measures_df.hd16 + measures_df.hd17 + measures_df.hd18 + ...
                 measures_df.hd19 + measures_df.hd20 + measures_df.hd21 + measures_df.hd22 + ...
                 measures_df.hd24) * 14 * 165) / 54200;
    measures_df.avg_power = avg_power;
    measures_df.thick1 = measures_df.thick1 * 1000;
    measures_df.thick2 = measures_df.thick2 * 1000;
    rows_before = height(measures_df);
    measures_df = rmmissing(measures_df);
    rows_after = height(measures_df);
    disp(['Original rows: ', num2str(rows_before)]);
    disp(['Rows after cleaning: ', num2str(rows_after)]);
    disp(['Removed ', num2str(rows_before - rows_after), ' rows with NaN values']);
    zero_speed_rows = sum(measures_df.speed == 0);
    measures_df = measures_df(measures_df.speed > 0, :);
    disp(['Removed ', num2str(zero_speed_rows), ' rows with zero speed']);
    disp(['Final row count: ', num2str(height(measures_df))]);
    save(mat_filename, 'measures_df');
end

%% 2. 定义状态、输入、输出列（与原脚本一致）
 tube_temp_cols = cell(1, 25);
 for i = 1:25
     tube_temp_cols{i} = ['tt' num2str(i)];
 end
 state_cols = [tube_temp_cols, {'measureExitTemp'}];
 input_cols = {'avg_power', 'speed', 'thick1', 'width1', 'weldPercent1', ...
               'thick2', 'width2', 'weldPercent2', 'measureEntryTemp'};
 output_col = 'measureExitTemp';

%% 3. 数据划分（与原脚本一致）
offline_train_ratio = 0.7;
online_test_ratio = 0.1;
total_samples = height(measures_df);
offline_train_num = floor(total_samples * offline_train_ratio);
online_test_num = floor(total_samples * online_test_ratio);

%% 4. 数据准备（与原脚本一致）
X = table2array(measures_df(:, state_cols));
U = table2array(measures_df(:, input_cols));
Y = table2array(measures_df(:, output_col));
X_offline_train = X(1:offline_train_num, :);
U_offline_train = U(1:offline_train_num, :);
X_online_test = X(offline_train_num+1:offline_train_num+online_test_num, :);
U_online_test = U(offline_train_num+1:offline_train_num+online_test_num, :);
Y_online_test = Y(offline_train_num+1:offline_train_num+online_test_num, :);

%% 5. 离线辨识初始AB模型（与原脚本一致）
[A0, B0] = identify_ab_regularized(U_offline_train, X_offline_train);
C = zeros(1, length(state_cols));
C(1, end) = 1;

%% 6. 不同rls_update_interval下的自适应AB模型预测 (*** MODIFIED SECTION ***)
rls_update_intervals = [1, 10, 50];
Y_pred_online_all = zeros(online_test_num-1, length(rls_update_intervals));
lambda = 1e-3; % 正则化系数，用于批量最小二乘

for idx = 1:length(rls_update_intervals)
    rls_update_interval = rls_update_intervals(idx);
    fprintf('正在计算 块更新间隔 = %d ...\n', rls_update_interval);
    
    n_x = size(X_offline_train, 2);
    n_u = size(U_offline_train, 2);
    
    % 初始化模型参数
    A_k = A0;
    B_k = B0;
    x_current = X_online_test(1, :)';
    
    %%% --- MODIFICATION START --- %%%
    % 初始化数据缓冲区，用于存储一个更新周期内的数据
    phi_buffer = []; % 回归向量缓冲区
    x_next_buffer = []; % 下一状态真值缓冲区
    %%% --- MODIFICATION END --- %%%
    
    progress_step = floor((online_test_num-1)/10);
    for k = 1:online_test_num-1
        if mod(k, progress_step) == 0
            fprintf('  块间隔=%d: 已完成%.0f%%\n', rls_update_interval, k/(online_test_num-1)*100);
        end
        
        % 1. 使用当前模型进行预测
        u_k = U_online_test(k, :)';
        x_pred = A_k * x_current + B_k * u_k;
        
        %%% --- MODIFICATION START --- %%%
        % 2. 收集数据到缓冲区
        phi_k = [x_current; u_k];
        x_next_k = X_online_test(k+1, :)';
        phi_buffer = [phi_buffer; phi_k']; % 将列向量转置后按行存入
        x_next_buffer = [x_next_buffer; x_next_k'];
        
        % 3. 周期性进行批量更新
        % 在间隔末端或整个测试结束时触发更新
        if mod(k, rls_update_interval) == 0 || k == (online_test_num-1)
            
            % 确保缓冲区内有数据才更新
            if ~isempty(phi_buffer)
                PHI_batch = phi_buffer;
                X_next_batch = x_next_buffer;
                n_params = size(PHI_batch, 2);
                
                % 对模型的每一行独立进行批量最小二乘更新
                for i = 1:n_x
                    Y_batch_i = X_next_batch(:, i);
                    
                    % 求解正则化最小二乘问题: theta = (PHI'*PHI + lambda*I)^-1 * (PHI'*Y)
                    theta_i = (PHI_batch' * PHI_batch + lambda * eye(n_params)) \ (PHI_batch' * Y_batch_i);
                    
                    % 更新模型参数 A_k 和 B_k 的对应行
                    A_k(i, :) = theta_i(1:n_x)';
                    B_k(i, :) = theta_i(n_x+1:end)';
                end
                
                % 清空缓冲区，为下一个周期做准备
                phi_buffer = [];
                x_next_buffer = [];
            end
        end
        %%% --- MODIFICATION END --- %%%
        
        % 4. 存储本次预测的输出值
        Y_pred_online_all(k, idx) = C * x_pred;
        
        % 5. 更新状态向量以备下次迭代使用
        if k < online_test_num-1
            x_current(1:end-1) = X_online_test(k+1, 1:end-1)';
            x_current(end) = x_pred(end); % 使用模型预测的出口温度作为下一时刻状态的一部分
        end
    end
    fprintf('块间隔=%d 计算完成！\n', rls_update_interval);
end

%% 7. 误差与性能指标（与原脚本一致）
error_online_all = Y_online_test(1:end-1) - Y_pred_online_all;
rmse_online = sqrt(mean(error_online_all.^2));
mae_online = mean(abs(error_online_all));

%% 8. 绘图对比（与原脚本一致）
plot_range = [floor(0.6 * 3600 / 5), floor(1.4 * 3600 / 5)];
plot_compare_rls_update(Y_online_test(1:end-1), Y_pred_online_all, error_online_all, rmse_online, plot_range, rls_update_intervals);

%% --- 子函数 --- （与原脚本一致）
function [A, B] = identify_ab_regularized(U, X)
    X_k = X(1:end-1, :);
    X_k1 = X(2:end, :);
    U_k = U(1:end-1, :);
    n_x = size(X, 2);
    n_u = size(U, 2);
    A = zeros(n_x, n_x);
    B = zeros(n_x, n_u);
    lambda = 1e-3;
    for i = 1:n_x
        PHI = [X_k, U_k];
        Y = X_k1(:, i);
        n_params = size(PHI, 2);
        theta = (PHI' * PHI + lambda * eye(n_params)) \ (PHI' * Y);
        A(i, :) = theta(1:n_x)';
        B(i, :) = theta(n_x+1:end)';
    end
end

function plot_compare_rls_update(Y_true, Y_online_all, error_online_all, rmse_online, plot_range, rls_update_intervals)
    font_size = 12;
    small_font_size = 9;
    colors = {'#D95319', '#EDB120', '#7E2F8E'};
    line_styles = {'-', '--', '-.'};
    min_len = min([length(Y_true), size(Y_online_all,1)]);
    Y_true = Y_true(1:min_len);
    Y_online_all = Y_online_all(1:min_len, :);
    error_online_all = error_online_all(1:min_len, :);
    plot_range(1) = max(1, min(plot_range(1), min_len));
    plot_range(2) = min(min_len, max(plot_range(2), plot_range(1)));
    idx_range = plot_range(1):plot_range(2);
    Y_true_plot = Y_true(idx_range);
    Y_online_plot = Y_online_all(idx_range, :);
    error_online_plot = error_online_all(idx_range, :);
    time_h = (0:(length(idx_range)-1))' * 5/3600;
    figure('Position', [100, 100, 1000, 300]);
    set(gcf, 'Color', 'w');
    subplot(1,2,1);
    plot(time_h, Y_true_plot, 'k-', 'LineWidth', 2, 'DisplayName', 'Actual Temperature'); hold on;
    for i = 1:size(Y_online_plot,2)
        plot(time_h, Y_online_plot(:,i), 'LineStyle', line_styles{i}, 'LineWidth', 2, 'Color', colors{i}, ...
            'DisplayName', sprintf('Update Interval=%d', rls_update_intervals(i)));
    end
    xlabel('Time (h)', 'FontSize', font_size);
    ylabel('Temperature (°C)', 'FontSize', font_size);
    xlim([0, max(time_h)]);
    legend('Location', 'northeast', 'FontSize', font_size);
    grid on;
    set(gca, 'LineWidth', 1.5, 'FontSize', font_size);
    subplot(1,2,2);
    for i = 1:size(error_online_plot,2)
        plot(time_h, error_online_plot(:,i), 'LineStyle', line_styles{i}, 'LineWidth', 2, 'Color', colors{i}, ...
            'DisplayName', sprintf('Interval=%d (RMSE=%.2f °C)', rls_update_intervals(i), rmse_online(i)));
        hold on;
    end
    yline(0, 'k--', 'LineWidth', 1, 'HandleVisibility', 'off');
    xlabel('Time (h)', 'FontSize', font_size);
    ylabel('Prediction Error (°C)', 'FontSize', font_size);
    xlim([0, max(time_h)]);
    ylim_val = max(abs(error_online_plot(:))) * 1.2;
    ylim([-ylim_val, ylim_val]);
    legend('Location', 'northeast', 'FontSize', font_size);
    grid on;
    set(gca, 'LineWidth', 1.5, 'FontSize', font_size);
    axes_pos = [0.71, 0.24, 0.18, 0.18];
    axes('Position', axes_pos);
    for i = 1:size(error_online_plot,2)
        [f_online, xi_online] = ksdensity(error_online_plot(:,i));
        plot(xi_online, f_online, 'LineStyle', line_styles{i}, 'LineWidth', 2, 'Color', colors{i}); hold on;
        xline(mean(error_online_plot(:,i)), '--', 'Color', colors{i}, 'LineWidth', 1);
    end
    ylabel({'Probability','Density'}, 'FontSize', small_font_size);
    title('Residual Distribution', 'FontSize', small_font_size);
    set(gca, 'FontSize', small_font_size, 'LineWidth', 1);
    grid on;
    if ~exist('./paper_plot', 'dir')
        mkdir('./paper_plot');
    end
    exportgraphics(gcf, './paper_plot/online_model_compare_block_update.png', 'Resolution', 300);
end
